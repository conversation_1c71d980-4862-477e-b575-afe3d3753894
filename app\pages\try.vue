<script setup>
import Grant<PERSON>hart from "../components/grant.vue";
import PssChart from "../components/pss.vue";
import RegistrationChart from "../components/registration.vue";
import { useState } from "#imports";
import Abyssinia_column from "~/components/abyssinia_first_trench_column.vue";
import Hibret_column from "~/components/hibret_second_trench_column.vue";
import DashenThirdTrenchColumn from "~/components/dashen_third_trench_column.vue";
import CountUp from "~/components/countup.vue";
import Hibret_first_trench_column from "~/components/hibret_first_trench_column.vue";
import IfbDisbursement from '~/components/ifb_disbursement.vue';
import YouthEmployment from '~/components/youth_employment.vue';
import IFBFOMTotalDisbursement from '~/components/ifb_fom_total_disbursement.vue';
import IFBRegistration from '~/components/ifb_registration.vue'
import DisabilityRegistration from '~/components/disability_registration.vue'
import FormalEnterprisesNumber from '~/components/formal_enterprises_number.vue'
import Y3Q2Column from '~/components/y3q2_column.vue'
import EnterprisesSupported from '~/components/enterprises_supported.vue'
import OutreachedIndividuals from '~/components/Outreached_Individuals.vue'
import InformalEnterprisesNumber from '~/components/informal_enterprises_number.vue'
import WomanEmploymentPie from '~/components/woman_employment_pie.vue'
import banks from '~/components/banks.vue'
import Y3q2Column from '~/components/y3q2_column.vue'
import Y3q2Pie from '~/components/y3q2_pie.vue'
import AnnualQ2Column from '~/components/annual_q2_column.vue'
import AnnualQ2Pie from '~/components/annual_q2_pie.vue'
import Y3Column from '~/components/y3_column.vue'
import Y3Pie from '~/components/y3_pie.vue'
import SecondY3q2Column from '~/components/second_y3q2_column.vue'
import SecondY3q2Pie from '~/components/second_y3q2_pie.vue'
import SecondAnnualQ2 from '~/components/second_annual_q2.vue'
import SecondAnnualQ2Pie from '~/components/second_annual_q2_pie.vue'
import IfbColumn from '~/components/ifb_column.vue'
import IfbPie from '~/components/ifb_pie.vue'
import Awash_first_trench_column from '~/components/awash_first_trench_column.vue'
import Dashen_first_trench_column from '~/components/dashen_first_trench_column.vue'
import AwashSecondTrenchColumn from '~/components/awash_second_trench_column.vue'
import DashenSecondTrenchColumn from '~/components/dashen_second_trench_column.vue'
import AwashThirdTrenchColumn from '~/components/awash_third_trench_column.vue'

const currentDate = useState("currentDate", () => {
  const today = new Date();
  const options = { day: "2-digit", month: "long", year: "numeric" };
  const formatted = today.toLocaleDateString("en-US", options);
  return formatted.toUpperCase();
});
</script>
<template>
  <div class="min-h-screen bg-gray-100 p-4">
    <!-- Header Section -->
    <div class="flex justify-center mb-6">
      <div class="bg-gradient-to-r from-teal-700 to-blue-700 rounded-xl p-1 w-full max-w-3xl">
        <h1 class="text-3xl font-bold font-serif text-white text-center tracking-wide">
          Dashboard, {{ currentDate }}
        </h1>
      </div>
    </div>

    <div class="flex justify-center mb-8">
      <div class="bg-white rounded-xl p-1 pt-4 w-full max-w-2xl">
        <h1 class="text-3xl font-bold font-serif text-black text-center tracking-wide">
          <p><span class="text-[#ff7f50]">MESMER</span> Supported</p>
        </h1>
      </div>
    </div>

    <!-- Main Grid -->
    <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-4 gap-6">

      <!-- Youth Employment Card -->
      <div class="bg-white rounded-2xl p-6 shadow flex flex-col items-center justify-center text-center h-48">
        <p class="text-lg font-semibold text-gray-600 mb-2">Youth Employment</p>
        <div class="bg-gray-100 border border-gray-300 px-6 py-3 rounded-xl shadow text-3xl font-mono font-bold text-gray-800">
          <YouthEmployment />
        </div>
        <p class="mt-2 text-sm font-medium text-gray-500">Employed</p>
      </div>

      <!-- Enterprises Card -->
      <div class="bg-white rounded-2xl p-6 shadow flex flex-col items-center justify-center text-center h-48">
        <p class="text-lg font-semibold text-gray-600 mb-2">Enterprises</p>
        <div class="bg-gray-100 border border-gray-300 px-6 py-3 rounded-xl shadow text-3xl font-mono font-bold text-gray-800">
          <EnterprisesSupported />
        </div>
        <p class="mt-2 text-sm font-medium text-gray-500">Supported</p>
      </div>

      <!-- Outreach Individuals Card -->
      <div class="bg-white rounded-2xl p-6 shadow flex flex-col items-center justify-center text-center h-48">
        <p class="text-lg font-semibold text-gray-600 mb-2">Outreach Individuals</p>
        <div class="bg-gray-100 border border-gray-300 px-6 py-3 rounded-xl shadow text-3xl font-mono font-bold text-gray-800">
          <OutreachedIndividuals />
        </div>
        <p class="mt-2 text-sm font-medium text-gray-500">Reached</p>
      </div>

      <!-- Woman Employment Card with Chart and Image -->
      <div class="bg-white rounded-2xl shadow p-6 flex flex-col items-center">
        <p class="text-lg font-semibold text-gray-600 mb-4">Woman Employment</p>
        <div class="flex flex-row items-center justify-center gap-4">
          <div class="w-[120px] h-[120px]">
            <ClientOnly>
              <WomanEmploymentPie />
            </ClientOnly>
          </div>
          <div class="w-[80px] h-[80px]">
            <img
              src="@/assets/mesmerLogo/businesswoman.png"
              alt="business woman"
              class="rounded-lg shadow w-full h-full object-contain"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Informal Enterprise Section -->
    <div class="max-w-7xl mx-auto mt-12">
      <div class="flex justify-center mb-6">
        <div class="bg-gradient-to-r from-teal-700 to-blue-700 shadow-xl rounded-xl p-1 w-full max-w-3xl">
          <h1 class="text-3xl font-bold font-serif text-white text-center tracking-wide">
            Informal Enterprise
          </h1>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Grant Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h2 class="text-2xl font-semibold mb-4 text-gray-800 text-center">Grant</h2>
          <div class="w-full h-full flex items-center justify-center">
            <client-only>
              <GrantChart />
            </client-only>
          </div>
        </div>

        <!-- PSS Card with Multiple Content -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h2 class="text-2xl font-semibold mb-4 text-gray-800 text-center">PSS</h2>
          <div class="w-full h-full flex items-center justify-between gap-x-4">
            <div class="w-1/2 flex items-center justify-center">
              <client-only>
                <PssChart />
              </client-only>
            </div>
            <div class="w-1/2 text-center space-y-4">
              <div class="bg-gray-100 border border-gray-300 px-4 py-2 rounded-lg">
                <div class="text-2xl font-bold text-gray-800">
                  <InformalEnterprisesNumber />
                </div>
                <p class="text-gray-700 text-sm">Informal Enterprises</p>
              </div>
              <div class="bg-gray-100 border border-gray-300 px-4 py-2 rounded-lg">
                <div class="text-2xl font-bold text-gray-800">
                  <FormalEnterprisesNumber />
                </div>
                <p class="text-gray-700 text-sm">Formal Enterprises</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Formal Enterprise Section -->
    <div class="max-w-7xl mx-auto mt-12">
      <div class="flex justify-center mb-6">
        <div class="bg-gradient-to-r from-teal-700 to-blue-700 shadow-xl rounded-xl p-1 w-full max-w-3xl">
          <h1 class="text-3xl font-bold font-serif text-white text-center tracking-wide">
            Formal Enterprise
          </h1>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
        <!-- Registration Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[300px] flex items-center justify-center">
          <client-only>
            <RegistrationChart />
          </client-only>
        </div>

        <!-- Banks Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[300px] lg:col-span-2 flex items-center justify-center">
          <client-only>
            <banks />
          </client-only>
        </div>

        <!-- IFB Registration & Disability Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[300px] flex flex-col items-center justify-center space-y-6">
          <div class="text-center">
            <p class="text-lg font-semibold text-black mb-2">Interest-Free Banking</p>
            <div class="bg-blue-200 border border-blue-500 text-blue-800 font-bold px-4 py-2 rounded-lg shadow">
              <IFBRegistration />
            </div>
          </div>
          <div class="text-center">
            <p class="text-lg font-semibold text-black mb-2">Disability Registration</p>
            <div class="bg-blue-200 border border-blue-500 text-blue-800 font-bold px-4 py-2 rounded-lg shadow">
              <DisabilityRegistration />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- BDS Status Section -->
    <div class="max-w-7xl mx-auto mt-12">
      <div class="flex justify-center mb-6">
        <div class="bg-gradient-to-r from-teal-700 to-blue-700 shadow-xl rounded-xl p-1 w-full max-w-3xl">
          <h1 class="text-3xl font-bold font-serif text-white text-center tracking-wide">
            BDS Status
          </h1>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Y3Q2 Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h3 class="text-lg font-extrabold mb-4 text-gray-800 text-center">Y3Q2</h3>
          <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <Y3q2Column />
              </client-only>
            </div>
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <Y3q2Pie />
              </client-only>
            </div>
          </div>
        </div>

        <!-- Annual Q2 Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h3 class="text-lg font-extrabold mb-4 text-gray-800 text-center">Annual Q2</h3>
          <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <AnnualQ2Column />
              </client-only>
            </div>
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <AnnualQ2Pie />
              </client-only>
            </div>
          </div>
        </div>

        <!-- Program Target vs Achievement Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h3 class="text-lg font-extrabold mb-4 text-gray-800 text-center">Program Target vs Achievement</h3>
          <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <Y3Column />
              </client-only>
            </div>
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <Y3Pie />
              </client-only>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Credit Disbursement Section -->
    <div class="max-w-7xl mx-auto mt-12">
      <div class="flex justify-center mb-6">
        <div class="bg-gradient-to-r from-teal-700 to-blue-700 shadow-xl rounded-xl p-1 w-full max-w-3xl">
          <h1 class="text-3xl font-bold font-serif text-white text-center tracking-wide">
            Credit Disbursement
          </h1>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- 2nd Y3Q2 Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h3 class="text-lg font-extrabold mb-4 text-gray-800 text-center">Y3Q2</h3>
          <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <SecondY3q2Column />
              </client-only>
            </div>
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <SecondY3q2Pie />
              </client-only>
            </div>
          </div>
        </div>

        <!-- 2nd Annual Q2 Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px]">
          <h3 class="text-lg font-extrabold mb-4 text-gray-800 text-center">Annual Q2</h3>
          <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <SecondAnnualQ2 />
              </client-only>
            </div>
            <div class="flex items-center justify-center bg-white rounded-lg p-4 min-h-[140px]">
              <client-only>
                <SecondAnnualQ2Pie />
              </client-only>
            </div>
          </div>
        </div>

        <!-- IFB Program Target vs Achievement Card -->
        <div class="bg-white rounded-2xl shadow p-6 min-h-[400px] space-y-4">
          <h3 class="text-lg font-extrabold text-gray-800 text-center">Program Target vs Achievement</h3>

          <!-- Top Charts -->
          <div class="w-full bg-white rounded-lg px-2 py-2 flex flex-col lg:flex-row items-center gap-4">
            <div class="w-[100px]">
              <IfbColumn />
            </div>
            <div class="flex-1 w-full">
              <client-only>
                <IfbPie />
              </client-only>
            </div>
          </div>

          <!-- Bottom Container -->
          <div class="w-full text-center bg-gray-50 rounded-lg p-4">
            <p class="text-lg font-semibold text-black mb-2">IFB Disbursement</p>
            <div class="bg-gray-200 border border-gray-500 text-black font-bold font-mono px-3 py-2 rounded-lg shadow text-lg inline-block">
              <IfbDisbursement />
            </div>
            <p class="mt-2 text-gray-700 text-sm">
              <span class="inline-flex items-center">
                <IFBFOMTotalDisbursement /><span>%</span>
              </span> of the total Disbursement
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Finance Section -->
    <div class="max-w-7xl mx-auto mt-12">
      <div class="flex justify-center mb-6">
        <div class="bg-gradient-to-r from-teal-700 to-blue-700 shadow-xl rounded-xl p-1 w-full max-w-3xl">
          <h1 class="text-3xl font-bold font-serif text-white text-center tracking-wide">
            Finance
          </h1>
        </div>
      </div>

      <!-- 1st Trench -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 text-center">1st Trench Disbursement</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <Abyssinia_column />
            </client-only>
          </div>
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <ClientOnly>
              <Hibret_first_trench_column />
            </ClientOnly>
          </div>
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <Awash_first_trench_column />
            </client-only>
          </div>
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <Dashen_first_trench_column />
            </client-only>
          </div>
        </div>
      </div>

      <!-- 2nd Trench -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 text-center">2nd Trench Disbursement</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <Hibret_column />
            </client-only>
          </div>
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <AwashSecondTrenchColumn />
            </client-only>
          </div>
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <DashenSecondTrenchColumn />
            </client-only>
          </div>
        </div>
      </div>

      <!-- 3rd Trench -->
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 text-center">3rd Trench Disbursement</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <AwashThirdTrenchColumn />
            </client-only>
          </div>
          <div class="bg-white rounded-2xl shadow p-6 flex items-center justify-center min-h-[200px]">
            <client-only>
              <DashenThirdTrenchColumn />
            </client-only>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
